#!/bin/bash

# Script to fix withAuth imports across all API route files

echo "Fixing withAuth imports..."

# Find all files that import from '@/lib/auth' instead of '@/lib/auth/server-exports'
files=$(grep -r "from '@/lib/auth'" src/app/api/ --include="*.ts" -l)

for file in $files; do
    echo "Processing: $file"
    
    # Replace the import statement
    sed -i.bak "s|from '@/lib/auth'|from '@/lib/auth/server-exports'|g" "$file"
    
    # Remove backup file
    rm "${file}.bak" 2>/dev/null || true
done

echo "Import fixes complete!"
echo "Note: You'll still need to manually fix the withAuth function calls to use the correct parameter order."
