import { ChatMessage } from "@langchain/core/messages";
import { BaseState, WorkflowConfig } from "../../../types/langgraph";

export class StateManager<T extends BaseState> {
  private state: T;
  private config: WorkflowConfig;

  constructor(initialState: T, config: WorkflowConfig = {}) {
    this.state = initialState;
    this.config = config;
  }

  getState(): T {
    return this.state;
  }

  updateState(newState: Partial<T>): void {
    this.state = { ...this.state, ...newState };
  }

  addMessage(message: ChatMessage): void {
    this.state.messages = [...this.state.messages, message];
  }

  getMessages(): ChatMessage[] {
    return this.state.messages;
  }

  getCurrentStep(): number {
    return this.state.current_step;
  }

  incrementStep(): void {
    this.state.current_step += 1;
  }

  getConfig(): WorkflowConfig {
    return this.config;
  }
}
