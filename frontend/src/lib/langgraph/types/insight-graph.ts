import { ChatMessage } from "@langchain/core/messages";
import { BaseState, WorkflowConfig } from "../../../types/langgraph";
import { Insight, Activity } from "../../types";

/**
 * Entities that can be analyzed by the insights system
 */
export type EntityType = 'case' | 'document' | 'deadline' | 'task' | 'client' | 'contact';

/**
 * Integration action types for bi-directional operations
 */
export type IntegrationActionType =
  // Case actions
  | 'GENERATE_CASE_INSIGHTS'
  | 'UPDATE_CASE_PRIORITY'
  | 'ADD_CASE_TASK'
  | 'UPDATE_CASE_STATUS'

  // Document actions
  | 'FIND_RELATED_DOCUMENTS'
  | 'GENERATE_DOCUMENT_INSIGHTS'
  | 'TAG_DOCUMENT'
  | 'ADD_DOCUMENT_COMMENT'

  // Calendar/Deadline actions
  | 'GENERATE_TIME_INSIGHTS'
  | 'CREATE_TASK_FROM_INSIGHT'
  | 'ADD_CALENDAR_EVENT'
  | 'GET_DEADLINE_RECOMMENDATIONS';

/**
 * Insight generation sources
 */
export type InsightSource = 'activity' | 'case' | 'document' | 'deadline' | 'user-query';

/**
 * Context object for supervisor agent
 */
export interface SupervisorContext {
  userId: string;
  tenantId: string;
  timestamp: string;
  userPreferences?: Record<string, any>;
  previousInsights?: Insight[];
  feedbackHistory?: Array<{
    insightId: string;
    rating: number;
    action?: string;
  }>;
}

/**
 * State for the insights graph
 */
export interface InsightGraphState extends BaseState {
  // Core LangGraph workflow properties
  messages: ChatMessage[];
  current_step: number;

  // Input data
  activities?: Activity[];
  relatedEntity?: {
    type: EntityType;
    id: string;
    name?: string;
    metadata?: Record<string, any>;
  };
  source: InsightSource;

  // Processing state
  activeAgents: string[];
  agentOutputs: Record<string, any>;

  // Agent collaboration data - NEW
  agentCollaboration?: {
    requests: Array<{
      fromAgent: string;
      toAgent: string;
      question: string;
      response?: string;
      timestamp: string;
    }>;
    sharedContext: Record<string, any>;
    votingResults?: Record<string, number>;
  };

  // Enhanced context information - NEW
  enhancedContext?: {
    entities: Record<string, any>;
    relationshipGraph: Record<string, string[]>;
    relevanceScores: Record<string, number>;
    externalKnowledge?: Record<string, any>;
  };

  // Output data
  generatedInsights: Insight[];
  recommendedActions: Array<{
    type: IntegrationActionType;
    entityType: EntityType;
    entityId?: string;
    data: Record<string, any>;
  }>;

  // Supervisor state
  supervisorFeedback?: {
    qualityScore: number;
    suggestedImprovements: string[];
    priorityAdjustments: Record<string, number>;
    // New fields for feedback on agent collaboration
    collaborationQuality?: number;
    insightConflictResolution?: Array<{
      conflictDescription: string;
      resolution: string;
      affectedAgents: string[];
    }>;
  };

  // Error handling
  errors: string[];

  // Tracking information
  systemPerformance: {
    startTime: string;
    endTime?: string;
    tokensUsed?: number;
    completionTime?: number;
    agentIterations: Record<string, number>;
    // Collaboration metrics - NEW
    collaborationMetrics?: {
      requestsCount: number;
      averageResponseTime: number;
      collaborationScore: number;
    };
  };
}

/**
 * Configuration for an insight workflow
 */
export interface InsightWorkflowConfig {
  maxAgents?: number;
  minInsightsRequired?: number;
  maxInsightsToReturn?: number;
  enableSupervisor?: boolean;
  enableSwarm?: boolean;
  supervisorCriteria?: {
    minQualityScore: number;
    requireActionability: boolean;
    checkForContradictions: boolean;
    enforceStyleGuide: boolean;
  };
  defaultProvider?: 'openai' | 'google';
  modelOverrides?: Record<string, string>;
}

/**
 * Result of an insight workflow execution
 */
export interface InsightWorkflowResult {
  insights: Insight[];
  recommendedActions: Array<{
    type: IntegrationActionType;
    entityType: EntityType;
    entityId?: string;
    data: Record<string, any>;
  }>;
  supervisorFeedback?: {
    qualityScore: number;
    suggestedImprovements: string[];
    priorityAdjustments: Record<string, number>;
  };
  performance: {
    startTime: string;
    endTime: string;
    completionTime: number;
    tokensUsed?: number;
    agentCounts: Record<string, number>;
  };
  errors: string[];
}
