/**
 * Redis utilities for tracking user activity
 * Used for proactive insights, session management, and inactivity detection
 */

import Redis from 'ioredis';
import { isProduction } from '../utils';

// Initialize Redis client
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
let redis: Redis | null = null;

// Only initialize Redis in production or if explicitly enabled for development
const useRedis = isProduction() || process.env.ENABLE_REDIS_DEV === 'true';

if (useRedis) {
  try {
    redis = new Redis(redisUrl);
    console.log('Redis client initialized for user activity tracking');

    // Handle Redis connection errors
    redis.on('error', (err: Error) => {
      console.error('Redis connection error:', err);
    });
  } catch (error) {
    console.error('Failed to initialize Redis client:', error);
    redis = null;
  }
}

/**
 * Get the last time a user performed a specific activity
 * @param userId User ID
 * @param activity Activity type (e.g., 'login', 'app_open', 'insight_generation')
 * @returns ISO timestamp string or null if no record exists
 */
export async function getUserLastActive(userId: string, activity: string): Promise<string | null> {
  if (!redis) {
    console.warn('Redis not available for getUserLastActive');
    return null;
  }

  try {
    const key = `user:${userId}:last_active:${activity}`;
    const timestamp = await redis.get(key);
    return timestamp;
  } catch (error) {
    console.error('Error getting user last active time:', error);
    return null;
  }
}

/**
 * Set the last time a user performed a specific activity
 * @param userId User ID
 * @param activity Activity type (e.g., 'login', 'app_open', 'insight_generation')
 * @param timestamp ISO timestamp string (defaults to current time)
 * @param expiryDays Number of days to keep this data (defaults to 30 days)
 */
export async function setUserLastActive(
  userId: string,
  activity: string,
  timestamp: string = new Date().toISOString(),
  expiryDays: number = 30
): Promise<boolean> {
  if (!redis) {
    console.warn('Redis not available for setUserLastActive');
    return false;
  }

  try {
    const key = `user:${userId}:last_active:${activity}`;
    const expirySeconds = expiryDays * 24 * 60 * 60; // Convert days to seconds

    await redis.set(key, timestamp, 'EX', expirySeconds);
    return true;
  } catch (error) {
    console.error('Error setting user last active time:', error);
    return false;
  }
}

/**
 * Get a map of all user activities and their last active times
 * @param userId User ID
 * @returns Object mapping activity types to ISO timestamp strings
 */
export async function getUserActivityMap(userId: string): Promise<Record<string, string>> {
  if (!redis) {
    console.warn('Redis not available for getUserActivityMap');
    return {};
  }

  try {
    const pattern = `user:${userId}:last_active:*`;
    const keys = await redis.keys(pattern);

    if (keys.length === 0) {
      return {};
    }

    const values = await redis.mget(keys);
    const result: Record<string, string> = {};

    keys.forEach((key: string, index: number) => {
      const activity = key.split(':').pop() as string;
      const timestamp = values[index];

      if (timestamp) {
        result[activity] = timestamp;
      }
    });

    return result;
  } catch (error) {
    console.error('Error getting user activity map:', error);
    return {};
  }
}

/**
 * Check if the user is currently active (has performed any activity recently)
 * @param userId User ID
 * @param maxInactiveMinutes Maximum inactivity period in minutes (default: 30 minutes)
 * @returns Boolean indicating if the user is active
 */
export async function isUserActive(userId: string, maxInactiveMinutes: number = 30): Promise<boolean> {
  if (!redis) {
    console.warn('Redis not available for isUserActive');
    return false;
  }

  try {
    // Check the generic 'app_open' activity first
    const lastActiveTimestamp = await getUserLastActive(userId, 'app_open');

    if (!lastActiveTimestamp) {
      return false;
    }

    const lastActive = new Date(lastActiveTimestamp).getTime();
    const now = Date.now();
    const maxInactiveMs = maxInactiveMinutes * 60 * 1000; // Convert minutes to milliseconds

    return (now - lastActive) < maxInactiveMs;
  } catch (error) {
    console.error('Error checking if user is active:', error);
    return false;
  }
}

/**
 * Record an app_open activity for a user
 * This is typically called when the user opens the application or refreshes the page
 * @param userId User ID
 */
export async function recordAppOpen(userId: string): Promise<void> {
  await setUserLastActive(userId, 'app_open');
}

/**
 * Get users who haven't received insights in a while
 * @param minInactiveDays Minimum days since last insight generation
 * @param limit Maximum number of users to return
 * @returns Array of user IDs
 */
export async function getUsersDueForInsights(
  minInactiveDays: number = 1,
  limit: number = 50
): Promise<string[]> {
  if (!redis) {
    console.warn('Redis not available for getUsersDueForInsights');
    return [];
  }

  try {
    const pattern = 'user:*:last_active:insight_generation';
    const keys = await redis.keys(pattern);

    if (keys.length === 0) {
      return [];
    }

    const now = Date.now();
    const minInactiveMs = minInactiveDays * 24 * 60 * 60 * 1000; // Convert days to milliseconds
    const userDueDates: Array<{ userId: string; inactiveMs: number }> = [];

    // Check each user's last insight generation time
    for (const key of keys) {
      const userId = key.split(':')[1];
      const timestamp = await redis.get(key);

      if (timestamp) {
        const lastGen = new Date(timestamp).getTime();
        const inactiveMs = now - lastGen;

        if (inactiveMs >= minInactiveMs) {
          userDueDates.push({ userId, inactiveMs });
        }
      }
    }

    // Sort by most inactive first and limit the results
    return userDueDates
      .sort((a, b) => b.inactiveMs - a.inactiveMs)
      .slice(0, limit)
      .map(item => item.userId);
  } catch (error) {
    console.error('Error getting users due for insights:', error);
    return [];
  }
}
