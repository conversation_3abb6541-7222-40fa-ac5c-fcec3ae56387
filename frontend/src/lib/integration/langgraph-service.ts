import { InsightWorkflow } from "../langgraph/workflows/insight-workflow";
import { AuthUser, Insight, Activity } from "../types";
import { EntityType } from "../langgraph/types/insight-graph";
import { logActivityToNeo4j } from "../neo4j/client";

/**
 * Service for integrating LangGraph-powered insights with the rest of the application
 */
export class LangGraphInsightService {
  private user: AuthUser;

  constructor(user: AuthUser) {
    this.user = user;
  }

  /**
   * Generate insights from user activities using LangGraph
   */
  async generateInsightsFromActivities(
    activities: Activity[],
    options: {
      useSupervisor?: boolean;
      useSwarm?: boolean;
      maxResults?: number;
      provider?: 'openai' | 'google';
    } = {}
  ): Promise<{
    insights: Insight[];
    performance: any;
  }> {
    try {
      const workflow = new InsightWorkflow(this.user, {
        enableSupervisor: options.useSupervisor !== false,
        enableSwarm: options.useSwarm !== false,
        maxInsightsToReturn: options.maxResults || 5,
        defaultProvider: options.provider || 'openai',
      });

      // Execute the workflow
      const result = await workflow.generateInsightsFromActivities(activities, {
        maxResults: options.maxResults,
      });

      // Log this to Neo4j
      await this.logInsightGeneration('activity', undefined, {
        insightCount: result.insights.length,
        activityCount: activities.length,
        performance: result.performance,
        usedSupervisor: options.useSupervisor !== false,
        usedSwarm: options.useSwarm !== false,
      });

      return {
        insights: result.insights,
        performance: result.performance,
      };
    } catch (error) {
      console.error("Error generating insights with LangGraph:", error);
      throw error;
    }
  }

  /**
   * Generate insights for a specific entity using LangGraph
   */
  async generateInsightsForEntity(
    entityType: EntityType,
    entityId: string,
    entityData: any,
    options: {
      activities?: Activity[];
      useSupervisor?: boolean;
      useSwarm?: boolean;
      maxResults?: number;
      provider?: 'openai' | 'google';
    } = {}
  ): Promise<{
    insights: Insight[];
    performance: any;
  }> {
    try {
      const workflow = new InsightWorkflow(this.user, {
        enableSupervisor: options.useSupervisor !== false,
        enableSwarm: options.useSwarm !== false,
        maxInsightsToReturn: options.maxResults || 5,
        defaultProvider: options.provider || 'openai',
      });

      // Execute the workflow
      const result = await workflow.generateInsightsForEntity(
        entityType,
        entityId,
        entityData,
        {
          activities: options.activities || [],
          maxResults: options.maxResults,
        }
      );

      // Log this to Neo4j
      await this.logInsightGeneration(entityType, entityId, {
        insightCount: result.insights.length,
        activityCount: options.activities?.length || 0,
        performance: result.performance,
        usedSupervisor: options.useSupervisor !== false,
        usedSwarm: options.useSwarm !== false,
      });

      return {
        insights: result.insights,
        performance: result.performance,
      };
    } catch (error) {
      console.error(`Error generating insights for ${entityType} with LangGraph:`, error);
      throw error;
    }
  }

  /**
   * Log insight generation to Neo4j
   */
  private async logInsightGeneration(
    entityType: string,
    entityId?: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    try {
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'LANGGRAPH_INSIGHT_GENERATION',
        entityType,
        entityId: entityId || 'unknown',
        metadata: {
          ...details,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("Error logging insight generation:", error);
      // Don't throw - this is a non-critical operation
    }
  }
}
