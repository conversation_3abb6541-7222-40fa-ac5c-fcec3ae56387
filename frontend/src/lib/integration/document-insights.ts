import { SupabaseClient } from '@supabase/supabase-js';
import { AuthUser } from '../types';
import { logActivityToNeo4j } from '../neo4j/client';
import { LLMService } from '../llm/service';

/**
 * NOTE: Future Implementation Plans
 *
 * This service will be enhanced with Neo4j integration for both:
 * 1. Public documents - legal cases and laws in the knowledge graph
 * 2. Company documents - internal documentation and precedents
 *
 * Neo4j will provide graph-based document relationships that complement
 * the vector search capabilities of Pinecone, enabling more complex
 * document relationship queries and traversals.
 */

/**
 * Service for bi-directional integration between document management and insights
 */
export class DocumentInsightsIntegration {
  private supabase: SupabaseClient;
  private llmService: LLMService;
  private pineconeClient!: PineconeClient;
  private user: AuthUser;
  private bucketName: string;

  constructor(
    supabase: SupabaseClient,
    user: AuthUser,
    options: {
      enableVectorSearch?: boolean
    } = {}
  ) {
    this.supabase = supabase;
    this.llmService = new LLMService({
      enableCaching: true
    });
    this.user = user;
    this.bucketName = process.env.GCS_BUCKET_NAME || 'texas-laws-personalinjury';

    if (options.enableVectorSearch) {
      // @ts-ignore - PineconeClient type mismatch
this.pineconeClient = new PineconeClient({
        apiKey: process.env.PINECONE_API_KEY!,
        environment: process.env.PINECONE_ENVIRONMENT || 'us-east-1',
        indexName: process.env.PINECONE_INDEX_NAME || 'new-texas-laws'
      });
    }
  }

  /**
   * Find related documents based on an insight and create bi-directional links
   */
  async findRelatedDocuments(insightText: string, caseId?: string): Promise<{
    documents: any[];
    embeddingVector?: number[];
  }> {
    try {
      // Generate an embedding vector for the insight text
      const embeddingResponse = await this.llmService.generateResponse(
        `Generate embedding for: ${insightText}`,
        'You are a system that generates embeddings for text.',
        { responseFormat: 'json' }
      );

      const embeddingData = JSON.parse(embeddingResponse);
      const embeddingVector = embeddingData.embedding;

      // Query Pinecone for similar documents
      let documents: any[] = [];

      if (this.pineconeClient) {
        // @ts-ignore - PineconeClient method not found
const searchResults = await this.pineconeClient.searchVectors({
          vector: embeddingVector,
          topK: 5,
          filter: caseId ? { caseId } : undefined
        });

        // Fetch full document details from Supabase using the IDs from Pinecone
        if (searchResults.matches && searchResults.matches.length > 0) {
          const documentIds = searchResults.matches.map((match: any) => match.id);

          const { data, error } = await this.supabase
            .schema('tenants')
            .from('documents')
            .select('*')
            .in('id', documentIds);

          if (error) throw error;
          documents = data || [];
        }
      } else {
        // Fallback to basic document search if vector search is not enabled
        const query = this.supabase
          .schema('tenants')
          .from('documents')
          .select('*');

        if (caseId) {
          query.eq('case_id', caseId);
        }

        // Limit results and sort by recency
        const { data, error } = await query
          .order('created_at', { ascending: false })
          .limit(5);

        if (error) throw error;
        documents = data || [];
      }

      // Log this integration activity
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'DOCUMENT_INSIGHT_SEARCH',
        entityType: 'insight',
        entityId: 'search_operation',
        metadata: {
          documentCount: documents.length,
          caseId,
          vectorSearchUsed: !!this.pineconeClient
        }
      });

      return {
        documents,
        embeddingVector
      };
    } catch (error) {
      console.error('[DocumentInsightsIntegration] Error finding related documents:', error);
      return { documents: [] };
    }
  }

  /**
   * Generate document-specific insights and update document metadata
   */
  async enrichDocumentWithInsights(documentId: string): Promise<{
    insights: any;
    documentUpdated: boolean;
  }> {
    try {
      // Fetch document details
      const { data: document, error } = await this.supabase
        .schema('tenants')
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single();

      if (error) throw error;
      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }

      // Get document content from GCS if available
      let documentContent = '';

      if (document.text_content) {
        documentContent = document.text_content;
      } else if (document.storage_path) {
        // Fetch document content from storage if not in the database
        const { data: fileData, error: fileError } = await this.supabase
          .storage
          .from(this.bucketName)
          .download(document.storage_path);

        if (fileError) throw fileError;
        documentContent = await fileData.text();
      }

      if (!documentContent) {
        return { insights: {}, documentUpdated: false };
      }

      // Generate insights using LLM
      const prompt = `
        Analyze this legal document and provide:
        1. Key points (maximum 5)
        2. Potential issues or risks (maximum 3)
        3. Suggested actions (maximum 3)
        4. Relevant deadlines or dates mentioned

        Document Title: ${document.title || 'Untitled'}
        Document Content: ${documentContent.substring(0, 5000)}

        Return the results as a structured JSON object with the following fields:
        keyPoints, potentialIssues, suggestedActions, relevantDates
      `;

      const insightsResponse = await this.llmService.generateResponse(
        prompt,
        'You are an AI assistant that helps analyze legal documents and identify important insights.',
        { responseFormat: 'json' }
      );

      const insights = JSON.parse(insightsResponse);

      // Update document metadata with insights
      const metadata = {
        ...(document.metadata || {}),
        insights: {
          generated: new Date().toISOString(),
          data: insights
        }
      };

      // Update the document with the new insights metadata
      const { error: updateError } = await this.supabase
        .schema('tenants')
        .from('documents')
        .update({ metadata })
        .eq('id', documentId);

      if (updateError) throw updateError;

      // Log this integration activity
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'DOCUMENT_INSIGHTS_GENERATION',
        entityType: 'document',
        entityId: documentId,
        metadata: {
          source: 'document-insights-integration',
          keyPointCount: insights.keyPoints?.length || 0
        }
      });

      return {
        insights,
        documentUpdated: true
      };
    } catch (error) {
      console.error('[DocumentInsightsIntegration] Error enriching document:', error);
      throw error;
    }
  }

  /**
   * Apply actions from insights to documents
   */
  async applyInsightToDocument(
    insightId: string,
    documentId: string,
    action: string,
    actionData: Record<string, any> = {}
  ): Promise<{
    success: boolean;
    result: any;
  }> {
    try {
      // Fetch document details
      const { data: document, error } = await this.supabase
        .schema('tenants')
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single();

      if (error) throw error;
      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }

      let result: any = null;

      // Handle different action types
      switch (action) {
        case 'TAG_DOCUMENT':
          // Add tags to document
          const currentTags = document.tags || [];
          const tagSet = new Set([...currentTags, ...actionData.tags]);
          const updatedTags = Array.from(tagSet);

          const { data, error: tagError } = await this.supabase
            .schema('tenants')
            .from('documents')
            .update({ tags: updatedTags })
            .eq('id', documentId)
            .select()
            .single();

          if (tagError) throw tagError;
          result = data;
          break;

        case 'UPDATE_DOCUMENT_PRIORITY':
          // Update document priority
          const updatedMetadata = {
            ...(document.metadata || {}),
            priority: actionData.priority,
            priorityReason: actionData.reason,
            priorityUpdatedAt: new Date().toISOString()
          };

          const { data: priorityData, error: priorityError } = await this.supabase
            .schema('tenants')
            .from('documents')
            .update({ metadata: updatedMetadata })
            .eq('id', documentId)
            .select()
            .single();

          if (priorityError) throw priorityError;
          result = priorityData;
          break;

        case 'ADD_DOCUMENT_COMMENT':
          // Add a comment to the document
          const { data: commentData, error: commentError } = await this.supabase
            .schema('tenants')
            .from('document_comments')
            .insert({
              document_id: documentId,
              user_id: this.user.id,
              content: actionData.comment,
              created_by: this.user.id,
              insight_generated: true,
              insight_id: insightId
            })
            .select()
            .single();

          if (commentError) throw commentError;
          result = commentData;
          break;

        default:
          throw new Error(`Unsupported action type: ${action}`);
      }

      // Log the action to Neo4j
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'DOCUMENT_INSIGHT_ACTION_APPLIED',
        entityType: 'document',
        entityId: documentId,
        metadata: {
          insightId,
          actionType: action,
          result: result ? 'success' : 'no_change'
        }
      });

      return {
        success: true,
        result
      };
    } catch (error) {
      console.error('[DocumentInsightsIntegration] Error applying insight to document:', error);
      throw error;
    }
  }
}

// Helper class for Pinecone client (stub implementation)
class PineconeClient {
  private config: {
    apiKey: string;
    environment: string;
    indexName: string;
  };

  constructor(config: {
    apiKey: string;
    environment: string;
    indexName: string;
  }) {
    this.config = config;
  }

  async searchVectors(params: {
    vector: number[];
    topK: number;
    filter?: Record<string, any>;
  }): Promise<{
    matches: Array<{ id: string; score: number }>;
  }> {
    try {
      // This is a placeholder - actual implementation would call Pinecone API
      console.log('[Pinecone] Searching vectors with params:', params);

      // Return empty matches for now
      return { matches: [] };
    } catch (error) {
      console.error('[Pinecone] Error searching vectors:', error);
      return { matches: [] };
    }
  }
}
