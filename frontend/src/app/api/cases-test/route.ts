// @ts-nocheck - API route type issues
// frontend/src/app/api/cases-test/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth/server-exports';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// GET /api/cases-test - Simplified test endpoint for debugging cases table access
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Log the user tenant ID for debugging
    console.log('Test endpoint - fetching cases for tenant ID:', user.tenantId);

    // First try to check if the cases table exists in the tenants schema
    try {
      // Simplified query with no joins or complex select
      const { data, error, count } = await supabase
        .schema('tenants')
        .from('cases')
        .select('id, title, tenant_id', { count: 'exact' })
        .eq('tenant_id', user.tenantId)
        .limit(5);

      if (error) {
        console.error('Cases table access error:', JSON.stringify(error, null, 2));

        // Return detailed error information for debugging
        return NextResponse.json({
          error: 'Failed to fetch cases',
          details: error.message,
          code: error.code,
          hint: error.hint,
          query: {
            schema: 'tenants',
            table: 'cases',
            tenant_id: user.tenantId
          }
        }, { status: 500 });
      }

      // Return success with the minimal data for verification
      return NextResponse.json({
        success: true,
        message: 'Successfully fetched cases',
        count: count || 0,
        data: data || []
      });
    } catch (schemaError) {
      console.error('Schema-level error:', schemaError);
      return NextResponse.json({
        error: 'Schema error',
        details: schemaError instanceof Error ? schemaError.message : String(schemaError),
        stack: schemaError instanceof Error ? schemaError.stack : 'No stack available'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Unexpected error in cases test API:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack available'
    }, { status: 500 });
  }
});
