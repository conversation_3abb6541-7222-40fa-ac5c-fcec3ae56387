import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON>, AuthUser, UserR<PERSON> } from '@/lib/auth/server-exports';
import { createServices } from '@/lib/services';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { z } from 'zod';

// GET /api/cases/[id]/deadlines - Fetches deadlines for a specific case
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase, context) => {
    try {
      // Safely access the case ID from context
      const caseId = context.params?.id;
      if (!caseId || !z.string().uuid().safeParse(caseId).success) {
        return NextResponse.json({ error: 'Invalid or missing Case ID' }, { status: 400 });
      }

      // Ensure tenantId exists for case-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      // Extract query parameters (optional: add validation if needed)
      const { searchParams } = new URL(req.url);
      const queryOptions = {
        sortBy: searchParams.get('sortBy') || 'due_date',
        sortOrder: searchParams.get('sortOrder') || 'asc',
        filterByStatus: searchParams.get('status'),
        // Add pagination parameters if needed
        // page: parseInt(searchParams.get('page') || '1', 10),
        // limit: parseInt(searchParams.get('limit') || '10', 10),
      };

      // Create services with validated tenantId
      const services = createServices(supabase as TypedSupabaseClient, user.tenantId);

      // Fetch deadlines directly from the 'deadlines' table for this specific case
      const { data: deadlines, error, count } = await supabase
        .schema('tenants') // Ensure querying the correct schema if applicable
        .from('deadlines')
        .select('*', { count: 'exact' }) // Select all fields and get total count
        .eq('case_id', caseId)          // Filter by case ID
        .eq('tenant_id', user.tenantId); // Filter by tenant ID
        // Add .order() here if sorting is needed
        // Add .range() here if pagination is needed

      if (error) {
        console.error('Error fetching deadlines from DB:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      // Return deadlines and total count
      return NextResponse.json({ deadlines: deadlines ?? [], totalCount: count ?? 0 });

    } catch (error: unknown) {
      console.error('Error fetching deadlines:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      if (errorMessage.includes('Case not found')) {
         return NextResponse.json({ error: 'Case not found' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Failed to fetch deadlines', details: errorMessage }, { status: 500 });
    }
  }
);

// Zod schema for POST request body
const postDeadlineSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  due_date: z.string().datetime({ message: "Invalid datetime string! Must be UTC.", offset: true }), // Expect ISO 8601 string
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).optional().default('medium'),
  // Add assigned_to_user_id if needed, ensure it's a valid UUID
  // assigned_to_user_id: z.string().uuid().optional(),
});
type PostDeadlineRequestBody = z.infer<typeof postDeadlineSchema>;

// POST /api/cases/[id]/deadlines - Creates a new deadline for a case
export const POST = withAuth(
  async (req: NextRequest, user: AuthUser, supabase, context) => {
    try {
      // Safely access the case ID from context
      const caseId = context.params?.id;
      if (!caseId || !z.string().uuid().safeParse(caseId).success) {
        return NextResponse.json({ error: 'Invalid or missing Case ID' }, { status: 400 });
      }

      // Ensure tenantId exists for case-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      let requestBodyJson;
      try {
        requestBodyJson = await req.json();
      } catch (parseError) {
        return NextResponse.json({ error: 'Invalid JSON format in request body' }, { status: 400 });
      }

      // Validate request body
      const parsedBody = postDeadlineSchema.safeParse(requestBodyJson);
      if (!parsedBody.success) {
        return NextResponse.json({ error: 'Invalid request body', details: parsedBody.error.errors }, { status: 400 });
      }

      // Corrected due_date type to string (ISO format expected by service)
      const deadlineData: PostDeadlineRequestBody & { case_id: string; created_by_user_id: string; status: 'pending' } = {
        ...parsedBody.data,
        due_date: new Date(parsedBody.data.due_date).toISOString(), // Convert back to ISO string
        case_id: caseId,
        status: 'pending', // Add default status explicitly
        created_by_user_id: user.id,
      };

      // Create services with validated tenantId
      const services = createServices(supabase as TypedSupabaseClient, user.tenantId);

      let newDeadline;
      try {
        // Assume services.deadlines.create returns the object directly or throws
        newDeadline = await services.deadlines.create(user.id, deadlineData);
      } catch (createError) {
        console.error('Error creating deadline via service:', createError);
        const serviceErrorMessage = createError instanceof Error ? createError.message : 'Failed to create deadline via service';
         // Check for specific errors if needed, e.g., permission denied
        return NextResponse.json({ error: 'Failed to create deadline', details: serviceErrorMessage }, { status: 500 });
      }

      return NextResponse.json({ deadline: newDeadline }, { status: 201 });

    } catch (error: unknown) {
      console.error('Error processing POST request for deadline:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during deadline creation';
       if (errorMessage.includes('Case not found')) {
         return NextResponse.json({ error: 'Case not found' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Internal Server Error', details: errorMessage }, { status: 500 });
    }
  }
);
