import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON>, AuthUser, UserR<PERSON> } from '@/lib/auth/server-exports';
import { createServices } from '@/lib/services';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { z } from 'zod';

/**
 * GET /api/cases/[id]/documents - Fetch all documents for a specific case
 *
 * Query parameters:
 * - type: Filter by document type
 * - status: Filter by processing status
 * - search: Search term for document title/content
 * - page: Page number for pagination
 * - limit: Number of documents per page
 * - sortBy: Field to sort by (default: 'created_at')
 * - sortOrder: 'asc' or 'desc' (default: 'desc')
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase, context) => {
    try {
      if (!context || !context.params) {
        return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
      }

      const params = context.params; // No need to await here
      const { id: caseId } = params;

      if (!caseId || !z.string().uuid().safeParse(caseId).success) {
        return NextResponse.json({ error: 'Invalid case ID' }, { status: 400 });
      }

      // Ensure tenantId exists for case-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      // Create services with validated tenantId
      const services = createServices(supabase as TypedSupabaseClient, user.tenantId);

      // Extract query parameters
      const { searchParams } = new URL(req.url);
      const documentType = searchParams.get('type') || undefined;
      const status = searchParams.get('status') || undefined;
      const searchTerm = searchParams.get('search') || undefined;
      const page = parseInt(searchParams.get('page') || '1', 10);
      const limit = parseInt(searchParams.get('limit') || '20', 10);
      const sortBy = searchParams.get('sortBy') || 'created_at';
      const sortOrder = searchParams.get('sortOrder') === 'asc' ? 'asc' : 'desc';

      try {
        // First check if the case exists and user has access
        await services.cases.getById(caseId);

        // Get documents associated with the case
        const result = await services.documents.list({
          caseId,
          documentType,
          status,
          searchTerm,
          page,
          limit,
          sortBy,
          sortOrder,
        });

        // Return paginated results
        return NextResponse.json({
          documents: result.data,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(result.total / limit),
            totalCount: result.total,
            hasMore: page * limit < result.total
          }
        });
      } catch (error: unknown) {
        if (error instanceof Error && error.message === 'Case not found') {
          return NextResponse.json({ error: 'Case not found' }, { status: 404 });
        }
        throw error;
      }
    } catch (error: unknown) {
      console.error('Error in GET /api/cases/[id]/documents:', error);
      return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
    }
  }
);

/**
 * POST /api/cases/[id]/documents - Associate existing documents with a case
 *
 * Request body:
 * {
 *   document_ids: string[] - Array of document IDs to associate with the case
 * }
 */
export const POST = withAuth(
  async (req: NextRequest, user: AuthUser, supabase, context) => {
    try {
      if (!context || !context.params) {
        return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
      }

      const params = context.params; // No need to await
      const { id: caseId } = params;

      if (!caseId || !z.string().uuid().safeParse(caseId).success) {
        return NextResponse.json({ error: 'Invalid case ID' }, { status: 400 });
      }

      // Ensure tenantId exists for case-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      // Create services with validated tenantId
      const services = createServices(supabase as TypedSupabaseClient, user.tenantId);

      try {
        // First check if the case exists and user has access
        await services.cases.getById(caseId);

        // Get request body
        const body = await req.json();

        // Validate document IDs
        const schema = z.object({
          document_ids: z.array(z.string().uuid('Invalid document ID'))
        });

        const { document_ids } = schema.parse(body);

        if (!document_ids || document_ids.length === 0) {
          return NextResponse.json({ error: 'No document IDs provided' }, { status: 400 });
        }

        // Associate documents with the case
        const results = await Promise.all(
          document_ids.map(async (documentId) => {
            try {
              await services.documents.updateMetadata(documentId, { case_id: caseId }, user.id);
              return { id: documentId, success: true };
            } catch (err: unknown) {
              const errorMessage = err instanceof Error ? err.message : 'Unknown error associating document';
              return { id: documentId, success: false, error: errorMessage };
            }
          })
        );

        // Check if any operation failed
        const allSucceeded = results.every(result => result.success);

        if (allSucceeded) {
          return NextResponse.json({
            success: true,
            message: `${results.length} documents associated with case`,
            results
          });
        } else {
          // Some operations failed, return partial success
          return NextResponse.json({
            success: false,
            message: 'Some documents could not be associated with case',
            results
          }, { status: 207 }); // 207 Multi-Status
        }
      } catch (error: unknown) {
        if (error instanceof Error && error.message === 'Case not found') {
          return NextResponse.json({ error: 'Case not found' }, { status: 404 });
        } else if (error instanceof z.ZodError) {
          return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
        }
        throw error;
      }
    } catch (error: unknown) {
      console.error('Error in POST /api/cases/[id]/documents:', error);
      if (error instanceof z.ZodError) {
        return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
      }
      return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
    }
  }
);
