// @ts-nocheck - API route type issues
import { NextResponse } from 'next/server'
import { withAuth, createServiceClient } from '@/lib/auth/server-exports'

export const POST = withAuth(async (req, user, _supabase, _context) => {
  try {
    const {
      name,
      stateBarNumber,
      firmType,
      yearEstablished,
      primaryEmail,
      phone,
      websiteUrl,
      address,
      practiceAreas
    } = await req.json()

    // Create service client to check for existing firm
    const serviceClient = createServiceClient(req);

    // Check if a firm already exists for this tenant
    const { data: existingFirm, error: checkError } = await serviceClient
      .schema('tenants')
      .from('firms')
      .select('id')
      .eq('tenant_id', user.tenantId)
      .maybeSingle()

    if (checkError) {
      console.error('Error checking for existing firm:', checkError)
      // Decide if we should proceed or return an error
      // For now, let's return a server error
      return NextResponse.json({ error: 'Error checking firm existence' }, { status: 500 })
    }

    if (existingFirm) {
      // Firm already exists, return conflict error
      return NextResponse.json({ error: 'Firm already exists for this tenant' }, { status: 409 })
    }

    // Basic validation
    if (!name || !stateBarNumber || !firmType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Prepare payload
    const insertData = {
      tenant_id: user.tenantId,
      admin_user_id: user.id,
      name,
      state_bar_number: stateBarNumber,
      firm_type: firmType,
      year_established: yearEstablished,
      primary_email: primaryEmail,
      phone,
      website_url: websiteUrl,
      address,
      practice_areas: practiceAreas,
      status: 'active',
      verification_status: 'pending',
      subscription_tier: 'basic',
      subscription_status: 'active'
    }

    // Call Supabase REST API directly with service role
    // NOTE: We could potentially use the serviceClient created above,
    // but the original code used fetch directly. Keeping it for now.
    const serviceKey = process.env.SUPABASE_SERVICE_KEY!
    const url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/firms`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        apikey: serviceKey,
        Authorization: `Bearer ${serviceKey}`,
        'Content-Type': 'application/json',
        'Content-Profile': 'tenants',
        Prefer: 'return=representation'
      },
      body: JSON.stringify(insertData)
    })

    const data = await response.json()

    if (!response.ok) {
      // Log the specific Supabase error if available
      console.error('Supabase firm creation error:', data);
      return NextResponse.json({ error: data?.message || JSON.stringify(data) }, { status: response.status })
    }

    return NextResponse.json({ firm: data[0] }, { status: 201 })
  } catch (err) {
    console.error('Create firm error:', err)
    return NextResponse.json({ error: 'Server error' }, { status: 500 })
  }
})
