'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/auth/useAuth';

/**
 * Calendar Connect Page
 * 
 * This page allows users to connect their calendars to the system.
 * It provides options to connect Google Calendar, Microsoft Outlook, and Calendly.
 */
export default function CalendarConnectPage() {
  const [provider, setProvider] = useState<string>('google');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/login?redirect=/calendar/connect');
    }
  }, [user, router]);

  // Handle connect button click
  const handleConnect = async () => {
    setIsLoading(true);
    
    try {
      // Call the API to initiate OAuth flow
      const response = await fetch(`/api/calendar/connect?provider=${provider}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to initiate calendar connection');
      }
      
      const data = await response.json();
      
      // Redirect to OAuth consent page
      window.location.href = data.authUrl;
    } catch (error) {
      console.error('Error connecting calendar:', error);
      toast({
        title: 'Error',
        description: 'Failed to connect calendar. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Connect Your Calendar</h1>
      
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Calendar Connection</CardTitle>
          <CardDescription>
            Connect your calendar to enable scheduling and availability features.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="provider">Calendar Provider</Label>
              <Select
                value={provider}
                onValueChange={setProvider}
              >
                <SelectTrigger id="provider">
                  <SelectValue placeholder="Select a provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="google">Google Calendar</SelectItem>
                  <SelectItem value="microsoft">Microsoft Outlook</SelectItem>
                  <SelectItem value="calendly">Calendly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {provider === 'google' && (
              <div className="text-sm text-muted-foreground">
                <p>Connecting Google Calendar will allow the system to:</p>
                <ul className="list-disc pl-5 mt-2">
                  <li>View your calendars and events</li>
                  <li>Create, update, and delete events</li>
                  <li>Check your availability</li>
                </ul>
              </div>
            )}
            
            {provider === 'microsoft' && (
              <div className="text-sm text-muted-foreground">
                <p>Connecting Microsoft Outlook will allow the system to:</p>
                <ul className="list-disc pl-5 mt-2">
                  <li>View your calendars and events</li>
                  <li>Create, update, and delete events</li>
                  <li>Check your availability</li>
                </ul>
              </div>
            )}
            
            {provider === 'calendly' && (
              <div className="text-sm text-muted-foreground">
                <p>Connecting Calendly will allow the system to:</p>
                <ul className="list-disc pl-5 mt-2">
                  <li>View your event types and scheduled events</li>
                  <li>Check your availability</li>
                  <li>Receive webhooks for new bookings</li>
                </ul>
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter>
          <Button 
            onClick={handleConnect} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Connecting...' : `Connect ${
              provider === 'google' ? 'Google Calendar' : 
              provider === 'microsoft' ? 'Microsoft Outlook' : 
              'Calendly'
            }`}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
