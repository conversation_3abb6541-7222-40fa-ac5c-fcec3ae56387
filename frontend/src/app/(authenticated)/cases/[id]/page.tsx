'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  ArrowLeft,
  Briefcase,
  FileText,
  Calendar,
  StickyNote,
  Loader2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch'

// Import case components
import { CaseOverview } from '@/components/cases/case-overview'
import { CaseDocuments } from '@/components/cases/case-documents'
import { CaseDeadlines } from '@/components/cases/case-deadlines'
import { CaseNotes } from '@/components/cases/case-notes'

// Case details page props
interface CaseDetailsPageProps {
  params: {
    id: string
  }
}

export default function CaseDetailsPage({ params }: CaseDetailsPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { authedFetch, isReady } = useAuthenticatedFetch()
  const caseId = params.id

  // Validate UUID format
  const isValidUUID = useCallback((uuid: string) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }, [])

  // State
  const [caseData, setCaseData] = useState<Record<string, unknown> | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')



  // Fetch case data using authenticated fetch
  const fetchCaseData = useCallback(async () => {
    if (!authedFetch || !isReady) {
      console.warn('CaseDetailsPage: authedFetch not ready for fetching case data');
      return;
    }

    // Check if case ID is a valid UUID before making the API call
    if (!isValidUUID(caseId)) {
      console.error('Invalid case ID format:', caseId);
      toast({
        title: 'Error',
        description: 'Invalid case ID format. Please check the URL and try again.',
        variant: 'destructive'
      });
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      // Fetch case data from API using authenticated fetch
      // authedFetch already handles JSON parsing and error checking
      const caseData = await authedFetch(`/api/cases/${caseId}`);
      setCaseData(caseData as Record<string, unknown>);
    } catch (error: unknown) {
      console.error('Error fetching case:', error);

      // Handle specific error cases
      const errorObj = error as { status?: number; message?: string };
      if (errorObj.status === 400 && errorObj.message?.includes('Invalid case ID')) {
        // This should not happen now that we validate the UUID format before making the API call
        // But keep it as a fallback
        toast({
          title: 'Error',
          description: 'Invalid case ID format. Please check the URL and try again.',
          variant: 'destructive'
        });
      } else {
        toast({
          title: 'Error',
          description: errorObj.message || 'Failed to load case details',
          variant: 'destructive'
        });
      }
    } finally {
      setLoading(false);
    }
  }, [caseId, authedFetch, isReady, toast, isValidUUID]);

  // Fetch data when component mounts or dependencies change
  useEffect(() => {
    fetchCaseData();
  }, [fetchCaseData])

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    // Update URL without refreshing the page
    const url = new URL(window.location.href)
    url.hash = value
    window.history.pushState({}, '', url.toString())
  }

  // Set initial tab based on URL hash if present
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash.replace('#', '')
      if (hash && ['overview', 'documents', 'deadlines', 'notes'].includes(hash)) {
        setActiveTab(hash)
      }
    }
  }, [])

  // Handle case edit
  const handleEditCase = () => {
    router.push(`/cases/${caseId}/edit`)
  }

  // Back to cases list
  const handleBackToCases = () => {
    router.push('/cases')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-16rem)]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <h3 className="text-lg font-medium">Loading case details...</h3>
        </div>
      </div>
    )
  }

  // Check if case ID is valid UUID format
  if (!isValidUUID(caseId)) {
    return (
      <div className="py-10 text-center">
        <h3 className="text-lg font-medium mb-2">Invalid Case ID</h3>
        <p className="text-muted-foreground mb-4">
          The case ID format is invalid. Please check the URL and try again.
        </p>
        <Button onClick={handleBackToCases}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Cases
        </Button>
      </div>
    )
  }

  if (!caseData) {
    return (
      <div className="py-10 text-center">
        <h3 className="text-lg font-medium mb-2">Case not found</h3>
        <p className="text-muted-foreground mb-4">
          The requested case could not be found or you don&apos;t have permission to view it.
        </p>
        <Button onClick={handleBackToCases}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Cases
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 max-w-screen-xl">
      {/* Header with back button */}
      <div className="mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4"
          onClick={handleBackToCases}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Cases
        </Button>
      </div>

      {/* Tabs Navigation */}
      <Tabs defaultValue={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full md:w-auto grid-cols-4 md:inline-grid">
          <TabsTrigger value="overview" className="flex items-center">
            <Briefcase className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center">
            <FileText className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Documents</span>
          </TabsTrigger>
          <TabsTrigger value="deadlines" className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Deadlines</span>
          </TabsTrigger>
          <TabsTrigger value="notes" className="flex items-center">
            <StickyNote className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Notes</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          <CaseOverview
            caseData={caseData}
            onEdit={handleEditCase}
          />
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <CaseDocuments
            caseId={caseId}
            caseName={caseData.title as string}
          />
        </TabsContent>

        <TabsContent value="deadlines" className="space-y-6">
          <CaseDeadlines
            caseId={caseId}
            caseName={caseData.title as string}
          />
        </TabsContent>

        <TabsContent value="notes" className="space-y-6">
          <CaseNotes
            caseId={caseId}
            caseName={caseData.title as string}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
