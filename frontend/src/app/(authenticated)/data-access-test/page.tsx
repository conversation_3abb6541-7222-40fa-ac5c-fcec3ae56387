'use client';

import { useState, useEffect } from 'react';
import { useDataAccess } from '@/hooks/useDataAccess';
import { useDataAccessTest } from '@/hooks/useDataAccessTest';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

/**
 * Test component for the data access layer
 */
export default function DataAccessTestPage() {
  const {
    isLoading: isLoadingData,
    error: dataError,
    getClients,
    getCases,
    getTasks,
    getSecurityEvents,
    getAuthAuditLogs
  } = useDataAccess();

  const {
    isLoading: isLoadingTest,
    error: testError,
    results: testResults,
    runTest
  } = useDataAccessTest();

  const [clients, setClients] = useState<any[]>([]);
  const [cases, setCases] = useState<any[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [securityEvents, setSecurityEvents] = useState<any[]>([]);
  const [authAuditLogs, setAuthAuditLogs] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('api-test'); // Default to API test tab

  // Function to load data for the active tab
  const loadData = async () => {
    try {
      switch (activeTab) {
        case 'api-test':
          // Run all tests via the API
          await runTest({ testType: 'all', limit: 10 });
          break;
        case 'clients':
          const clientsResult = await getClients({ limit: 10 });
          setClients((clientsResult as any)?.data || []);
          break;
        case 'cases':
          const casesResult = await getCases({ limit: 10 });
          setCases((casesResult as any)?.data || []);
          break;
        case 'tasks':
          const tasksResult = await getTasks({ limit: 10 });
          setTasks((tasksResult as any)?.data || []);
          break;
        case 'security-events':
          const securityEventsResult = await getSecurityEvents({ limit: 10 });
          setSecurityEvents((securityEventsResult as any)?.data || []);
          break;
        case 'auth-audit':
          const authAuditResult = await getAuthAuditLogs({ limit: 10 });
          setAuthAuditLogs((authAuditResult as any)?.data || []);
          break;
      }
    } catch (err) {
      console.error('Error loading data:', err);
    }
  };

  // Load data when the active tab changes
  useEffect(() => {
    loadData();
  }, [activeTab]);

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Data Access Layer Test</h1>

      {(dataError || testError) && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {dataError || testError}
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="api-test">API Test</TabsTrigger>
          <TabsTrigger value="clients">Clients</TabsTrigger>
          <TabsTrigger value="cases">Cases</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="security-events">Security Events</TabsTrigger>
          <TabsTrigger value="auth-audit">Auth Audit</TabsTrigger>
        </TabsList>

        <div className="mb-4">
          <Button onClick={loadData} disabled={isLoadingData || isLoadingTest}>
            {isLoadingData || isLoadingTest ? 'Loading...' : 'Refresh Data'}
          </Button>
        </div>

        <TabsContent value="api-test">
          <Card>
            <CardHeader>
              <CardTitle>Data Access API Test</CardTitle>
            </CardHeader>
            <CardContent>
              {!testResults ? (
                <p>Run the test to see results.</p>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <Badge variant={testResults.success ? "default" : "destructive"}>
                      {testResults.success ? "All Tests Passed" : "Tests Failed"}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      Timestamp: {new Date(testResults.timestamp).toLocaleString()}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(testResults.results).map(([name, result]) => (
                      <Card key={name} className="overflow-hidden">
                        <CardHeader className={`py-3 ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
                          <CardTitle className="text-sm font-medium flex items-center justify-between">
                            <span>{name}</span>
                            <Badge variant={result.success ? "default" : "destructive"}>
                              {result.success ? "Success" : "Failed"}
                            </Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">Count:</span>
                              <span className="text-sm">{result.count}</span>
                            </div>
                            {result.timing && (
                              <div className="flex justify-between">
                                <span className="text-sm font-medium">Time:</span>
                                <span className="text-sm">{result.timing}ms</span>
                              </div>
                            )}
                            {result.error && (
                              <div className="mt-2">
                                <span className="text-sm font-medium text-red-600">Error:</span>
                                <p className="text-sm text-red-600 mt-1">{result.error}</p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clients">
          <Card>
            <CardHeader>
              <CardTitle>Clients</CardTitle>
            </CardHeader>
            <CardContent>
              {clients.length === 0 ? (
                <p>No clients found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {clients.map((client) => (
                        <tr key={client.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{client.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {client.first_name} {client.last_name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{client.email}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{client.status}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cases">
          <Card>
            <CardHeader>
              <CardTitle>Cases</CardTitle>
            </CardHeader>
            <CardContent>
              {cases.length === 0 ? (
                <p>No cases found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {cases.map((case_) => (
                        <tr key={case_.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{case_.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{case_.title}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{case_.status}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(case_.created_at).toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks">
          <Card>
            <CardHeader>
              <CardTitle>Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              {tasks.length === 0 ? (
                <p>No tasks found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {tasks.map((task) => (
                        <tr key={task.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{task.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{task.title}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{task.status}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {task.due_date ? new Date(task.due_date).toLocaleString() : 'N/A'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security-events">
          <Card>
            <CardHeader>
              <CardTitle>Security Events</CardTitle>
            </CardHeader>
            <CardContent>
              {securityEvents.length === 0 ? (
                <p>No security events found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Severity</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {securityEvents.map((event) => (
                        <tr key={event.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{event.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{event.event_type}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{event.severity}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(event.created_at).toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="auth-audit">
          <Card>
            <CardHeader>
              <CardTitle>Auth Audit Logs</CardTitle>
            </CardHeader>
            <CardContent>
              {authAuditLogs.length === 0 ? (
                <p>No auth audit logs found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operation</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Time</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {authAuditLogs.map((log) => (
                        <tr key={log.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.operation}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {log.success ? 'Yes' : 'No'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {log.event_time ? new Date(log.event_time).toLocaleString() : 'N/A'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
