{"compilerOptions": {"target": "es2020", "lib": ["es2020", "dom"], "types": ["cypress", "node", "@types/cypress"], "strict": false, "noEmit": true, "isolatedModules": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "allowImportingTsExtensions": false, "moduleResolution": "node", "module": "esnext", "jsx": "react-jsx", "baseUrl": "../", "paths": {"@/*": ["src/*"]}, "typeRoots": ["../node_modules/@types", "./support"]}, "include": ["**/*.ts", "**/*.tsx", "support/**/*.ts", "support/**/*.d.ts"], "files": ["support/global.d.ts", "support/index.d.ts", "support/e2e.ts"], "exclude": ["node_modules", "../node_modules/cypress/react18"]}