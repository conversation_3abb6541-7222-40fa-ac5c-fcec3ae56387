import { createClient } from '@supabase/supabase-js';
import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// This test requires actual Supabase credentials
// It should be run in a test environment, not production
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Skip tests if credentials are not available
const skipTests = !supabaseUrl || !supabaseKey;

// Test data
const testTenantId = '00000000-0000-0000-0000-000000000001';
const testPlanId = '00000000-0000-0000-0000-000000000002';
const testAddonId = '00000000-0000-0000-0000-000000000003';

describe('Subscription Database Functions', () => {
  let supabase: any;
  let subscriptionId: string;

  // Setup before all tests
  beforeAll(async () => {
    if (skipTests) return;

    supabase = createClient(supabaseUrl, supabaseKey);

    // Create test data
    // 1. Create test plan if it doesn't exist
    const { data: existingPlan } = await supabase
      .from('subscription_plans')
      .select('id')
      .eq('id', testPlanId)
      .single();

    if (!existingPlan) {
      await supabase
        .from('subscription_plans')
        .insert({
          id: testPlanId,
          name: 'Test Plan',
          code: 'test_plan',
          description: 'Plan for testing',
          is_active: true,
          is_public: true,
          base_price_monthly: 99.99,
          base_price_yearly: 999.99,
          features: {
            maxUsers: 3,
            hasIntakeAgent: true,
            practiceAreas: ['Test'],
            states: ['Test'],
          },
        });
    }

    // 2. Create test addon if it doesn't exist
    const { data: existingAddon } = await supabase
      .from('subscription_addons')
      .select('id')
      .eq('id', testAddonId)
      .single();

    if (!existingAddon) {
      await supabase
        .from('subscription_addons')
        .insert({
          id: testAddonId,
          name: 'Test Addon',
          code: 'test_addon',
          description: 'Addon for testing',
          category: 'test',
          is_active: true,
          price_monthly: 19.99,
          price_yearly: 199.99,
          features: {
            additionalUsers: 5,
            hasCalendarAgent: true,
          },
        });
    }

    // 3. Create test firm if it doesn't exist
    const { data: existingFirm } = await supabase
      .from('firms')
      .select('tenant_id')
      .eq('tenant_id', testTenantId)
      .single();

    if (!existingFirm) {
      await supabase
        .from('firms')
        .insert({
          tenant_id: testTenantId,
          name: 'Test Firm',
          state_bar_number: 'TEST123',
          firm_type: 'Solo Practice',
          primary_email: '<EMAIL>',
          phone: '555-1234',
          address: {},
        });
    }

    // 4. Delete any existing subscriptions for the test tenant
    await supabase
      .from('tenant_subscriptions')
      .delete()
      .eq('tenant_id', testTenantId);

    // 5. Delete any existing tenant_quotas for the test tenant
    await supabase
      .from('tenant_quotas')
      .delete()
      .eq('tenant_id', testTenantId);
  });

  // Cleanup after all tests
  afterAll(async () => {
    if (skipTests) return;

    // Clean up test data
    await supabase
      .from('tenant_addons')
      .delete()
      .eq('tenant_id', testTenantId);

    await supabase
      .from('tenant_subscriptions')
      .delete()
      .eq('tenant_id', testTenantId);

    await supabase
      .from('tenant_quotas')
      .delete()
      .eq('tenant_id', testTenantId);
  });

  it.skipIf(skipTests)('should create a trial subscription', async () => {
    // Create a trial subscription
    const { data: subscription, error } = await supabase.rpc('create_trial_subscription', {
      p_tenant_id: testTenantId,
      p_plan_id: testPlanId,
      p_trial_days: 14,
    });

    // Store subscription ID for later tests
    subscriptionId = subscription.id;

    // Verify the result
    expect(error).toBeNull();
    expect(subscription).not.toBeNull();
    expect(subscription.tenant_id).toBe(testTenantId);
    expect(subscription.plan_id).toBe(testPlanId);
    expect(subscription.status).toBe('trialing');

    // Verify that tenant_quotas was updated
    const { data: quotas } = await supabase
      .from('tenant_quotas')
      .select('*')
      .eq('tenant_id', testTenantId)
      .single();

    expect(quotas).not.toBeNull();
    expect(quotas.subscription_id).toBe(subscription.id);
    expect(quotas.plan_tier).toBe('standard'); // Default from our function
  });

  it.skipIf(skipTests)('should check feature access', async () => {
    // Check access to a feature that exists in the plan
    const { data: hasAccess, error } = await supabase.rpc('check_tenant_feature_access', {
      p_tenant_id: testTenantId,
      p_feature_key: 'hasIntakeAgent',
    });

    // Verify the result
    expect(error).toBeNull();
    expect(hasAccess).toBe(true);

    // Check access to a feature that doesn't exist in the plan
    const { data: noAccess } = await supabase.rpc('check_tenant_feature_access', {
      p_tenant_id: testTenantId,
      p_feature_key: 'nonExistentFeature',
    });

    expect(noAccess).toBe(false);
  });

  it.skipIf(skipTests)('should add an addon to a subscription', async () => {
    // Add an addon to the subscription
    const { data: addon, error } = await supabase
      .from('tenant_addons')
      .insert({
        tenant_id: testTenantId,
        subscription_id: subscriptionId,
        addon_id: testAddonId,
        quantity: 1,
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      })
      .select()
      .single();

    // Verify the result
    expect(error).toBeNull();
    expect(addon).not.toBeNull();
    expect(addon.tenant_id).toBe(testTenantId);
    expect(addon.subscription_id).toBe(subscriptionId);
    expect(addon.addon_id).toBe(testAddonId);

    // Check access to a feature that exists in the addon
    const { data: hasAccess } = await supabase.rpc('check_tenant_feature_access', {
      p_tenant_id: testTenantId,
      p_feature_key: 'hasCalendarAgent',
    });

    expect(hasAccess).toBe(true);
  });

  it.skipIf(skipTests)('should extend a trial subscription', async () => {
    // Get the current trial end date
    const { data: beforeExtend } = await supabase
      .from('tenant_subscriptions')
      .select('trial_end, current_period_end')
      .eq('id', subscriptionId)
      .single();

    // Extend the trial
    const { data: extended, error } = await supabase.rpc('extend_subscription_trial', {
      p_subscription_id: subscriptionId,
      p_days: 7,
    });

    // Verify the result
    expect(error).toBeNull();
    expect(extended).toBe(true);

    // Get the updated trial end date
    const { data: afterExtend } = await supabase
      .from('tenant_subscriptions')
      .select('trial_end, current_period_end')
      .eq('id', subscriptionId)
      .single();

    // Verify that the trial was extended
    const beforeDate = new Date(beforeExtend.trial_end);
    const afterDate = new Date(afterExtend.trial_end);
    const diffDays = Math.round((afterDate.getTime() - beforeDate.getTime()) / (24 * 60 * 60 * 1000));

    expect(diffDays).toBe(7);
  });
});
