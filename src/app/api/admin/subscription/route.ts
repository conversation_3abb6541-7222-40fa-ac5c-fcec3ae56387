import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { createClient, isAuthenticatedLegacy, hasRoleLegacy } from '@/lib/auth/server-exports';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

/**
 * GET /api/admin/subscription
 * Get all subscription plans and addons
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = await createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(await supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticatedLegacy(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRoleLegacy(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const tenantId = searchParams.get('tenantId');
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // Get subscription plans
    const { data: plans, error: plansError } = await enhancedClient.tenants
      .from('subscription_plans')
      .select('*')
      .order('base_price_monthly', { ascending: true });

    if (plansError) {
      console.error('Error fetching subscription plans:', plansError);
      return NextResponse.json({ error: 'Failed to fetch subscription plans' }, { status: 500 });
    }

    // Get subscription addons
    const { data: addons, error: addonsError } = await enhancedClient.tenants
      .from('subscription_addons')
      .select('*')
      .order('price_monthly', { ascending: true });

    if (addonsError) {
      console.error('Error fetching subscription addons:', addonsError);
      return NextResponse.json({ error: 'Failed to fetch subscription addons' }, { status: 500 });
    }

    // Get tenant subscriptions if tenantId is provided
    let subscriptions = null;
    if (tenantId) {
      const { data: subs, error: subsError } = await enhancedClient.tenants
        .from('tenant_subscriptions')
        .select('*')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (subsError) {
        console.error('Error fetching tenant subscriptions:', subsError);
        return NextResponse.json({ error: 'Failed to fetch tenant subscriptions' }, { status: 500 });
      }

      subscriptions = subs;
    }

    return NextResponse.json({
      plans: plans.filter(plan => includeInactive || plan.is_active),
      addons: addons.filter(addon => includeInactive || addon.is_active),
      subscriptions
    });
  } catch (error) {
    console.error('Error in GET /api/admin/subscription:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/subscription
 * Create or update a subscription plan or addon
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = await createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(await supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticatedLegacy(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRoleLegacy(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { type, id, ...data } = body;

    if (!type || (type !== 'plan' && type !== 'addon')) {
      return NextResponse.json({ error: 'Invalid type. Must be "plan" or "addon"' }, { status: 400 });
    }

    const table = type === 'plan' ? 'subscription_plans' : 'subscription_addons';
    let result;

    if (id) {
      // Update existing plan or addon
      const { data: updatedItem, error } = await enhancedClient.tenants
        .from(table)
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error(`Error updating ${type}:`, error);
        return NextResponse.json({ error: `Failed to update ${type}` }, { status: 500 });
      }

      result = updatedItem;
    } else {
      // Create new plan or addon
      if (!data.code) {
        return NextResponse.json({ error: 'Code is required' }, { status: 400 });
      }

      if (!data.name) {
        return NextResponse.json({ error: 'Name is required' }, { status: 400 });
      }

      const { data: newItem, error } = await enhancedClient.tenants
        .from(table)
        .insert({
          ...data,
          is_active: data.is_active !== undefined ? data.is_active : true
        })
        .select()
        .single();

      if (error) {
        console.error(`Error creating ${type}:`, error);
        return NextResponse.json({ error: `Failed to create ${type}` }, { status: 500 });
      }

      result = newItem;
    }

    return NextResponse.json({
      success: true,
      [type]: result
    });
  } catch (error) {
    console.error('Error in POST /api/admin/subscription:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
