import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { createClient, isAuthenticatedLegacy, hasRoleLegacy } from '@/lib/auth/server-exports';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

/**
 * GET /api/admin/quota
 * Get tenant quotas and adjustments
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticatedLegacy(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRoleLegacy(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const tenantId = searchParams.get('tenantId');
    const usageType = searchParams.get('usageType');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Get tenant quotas
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(await supabase);

    let quotaQuery = enhancedClient.tenants
      .from('tenant_quotas')
      .select('*')
      .eq('tenant_id', tenantId);

    if (usageType) {
      quotaQuery = quotaQuery.eq('usage_type', usageType);
    }

    const { data: quotas, error: quotaError } = await quotaQuery;

    if (quotaError) {
      console.error('Error fetching quotas:', quotaError);
      return NextResponse.json({ error: 'Failed to fetch quotas' }, { status: 500 });
    }

    // Get quota adjustments
    const { data: adjustments, error: adjustmentError } = await enhancedClient.tenants
      .from('quota_adjustments')
      .select('*')
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: false });

    if (adjustmentError) {
      console.error('Error fetching adjustments:', adjustmentError);
      return NextResponse.json({ error: 'Failed to fetch quota adjustments' }, { status: 500 });
    }

    // Get effective quota limits
    const { data: effectiveQuotas, error: effectiveQuotaError } = await supabase
      .rpc('get_effective_quota_limit' as any, {
        p_tenant_id: tenantId,
        p_usage_type: usageType || null
      });

    if (effectiveQuotaError) {
      console.error('Error fetching effective quotas:', effectiveQuotaError);
      return NextResponse.json({ error: 'Failed to fetch effective quotas' }, { status: 500 });
    }

    return NextResponse.json({
      quotas,
      adjustments,
      effectiveQuotas
    });
  } catch (error) {
    console.error('Error in GET /api/admin/quota:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/quota
 * Create or update tenant quota
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticatedLegacy(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRoleLegacy(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { tenant_id, usage_type, monthly_limit, daily_limit, hourly_limit } = body;

    if (!tenant_id || !usage_type) {
      return NextResponse.json({ error: 'Tenant ID and usage type are required' }, { status: 400 });
    }

    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(await supabase);

    // Check if quota already exists
    const { data: existingQuota, error: checkError } = await enhancedClient.tenants
      .from('tenant_quotas')
      .select('*')
      .eq('tenant_id', tenant_id)
      .eq('usage_type', usage_type)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking existing quota:', checkError);
      return NextResponse.json({ error: 'Failed to check existing quota' }, { status: 500 });
    }

    let result;

    if (existingQuota) {
      // Update existing quota
      const { data, error } = await enhancedClient.tenants
        .from('tenant_quotas')
        .update({
          monthly_limit: monthly_limit !== undefined ? monthly_limit : existingQuota.monthly_limit,
          daily_limit: daily_limit !== undefined ? daily_limit : existingQuota.daily_limit,
          hourly_limit: hourly_limit !== undefined ? hourly_limit : existingQuota.hourly_limit,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingQuota.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating quota:', error);
        return NextResponse.json({ error: 'Failed to update quota' }, { status: 500 });
      }

      result = data;
    } else {
      // Create new quota
      const { data, error } = await enhancedClient.tenants
        .from('tenant_quotas')
        .insert({
          tenant_id,
          usage_type,
          monthly_limit: monthly_limit || null,
          daily_limit: daily_limit || null,
          hourly_limit: hourly_limit || null
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating quota:', error);
        return NextResponse.json({ error: 'Failed to create quota' }, { status: 500 });
      }

      result = data;
    }

    // Get effective quota limit
    const { data: effectiveQuota, error: effectiveQuotaError } = await supabase
      .rpc('get_effective_quota_limit' as any, {
        p_tenant_id: tenant_id,
        p_usage_type: usage_type
      });

    if (effectiveQuotaError) {
      console.error('Error fetching effective quota:', effectiveQuotaError);
      return NextResponse.json({ error: 'Failed to fetch effective quota' }, { status: 500 });
    }

    return NextResponse.json({
      quota: result,
      effectiveQuota: effectiveQuota[0]
    });
  } catch (error) {
    console.error('Error in POST /api/admin/quota:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/quota/adjustment
 * Create a quota adjustment
 */
export async function PATCH(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticatedLegacy(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRoleLegacy(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { tenant_id, usage_type, adjustment_amount, reason, expires_at } = body;

    if (!tenant_id || !usage_type || adjustment_amount === undefined) {
      return NextResponse.json({ error: 'Tenant ID, usage type, and adjustment amount are required' }, { status: 400 });
    }

    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(await supabase);

    // Create quota adjustment
    const { data, error } = await supabase
      .rpc('add_quota_adjustment' as any, {
        p_tenant_id: tenant_id,
        p_usage_type: usage_type,
        p_adjustment_amount: adjustment_amount,
        p_reason: reason || null,
        p_expires_at: expires_at || null
      });

    if (error) {
      console.error('Error creating quota adjustment:', error);
      return NextResponse.json({ error: 'Failed to create quota adjustment' }, { status: 500 });
    }

    // Get updated quota adjustments
    const { data: adjustments, error: adjustmentError } = await enhancedClient.tenants
      .from('quota_adjustments')
      .select('*')
      .eq('tenant_id', tenant_id)
      .eq('usage_type', usage_type)
      .order('created_at', { ascending: false });

    if (adjustmentError) {
      console.error('Error fetching adjustments:', adjustmentError);
      return NextResponse.json({ error: 'Failed to fetch quota adjustments' }, { status: 500 });
    }

    // Get effective quota limit
    const { data: effectiveQuota, error: effectiveQuotaError } = await supabase
      .rpc('check_quota_with_adjustments' as any, {
        p_tenant_id: tenant_id,
        p_usage_type: usage_type
      });

    if (effectiveQuotaError) {
      console.error('Error fetching effective quota:', effectiveQuotaError);
      return NextResponse.json({ error: 'Failed to fetch effective quota' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      adjustments,
      effectiveQuota: effectiveQuota[0]
    });
  } catch (error) {
    console.error('Error in PATCH /api/admin/quota/adjustment:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
