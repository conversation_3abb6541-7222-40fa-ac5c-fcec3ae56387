# fly.toml file for LangGraph FastAPI application with AG-UI protocol support
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.

app = "pi-lawyer-langgraph"
primary_region = "iad" # US East (N. Virginia)
kill_signal = "SIGINT"
kill_timeout = 30 # Allow 30 seconds for graceful shutdown

[build]
  dockerfile = "Dockerfile.backend" # Updated to use our specific Dockerfile

[env]
  PORT = "8000"
  # Default to production environment
  APP_ENV = "production"
  # Set Python to production mode
  PYTHONUNBUFFERED = "1"
  # Disable bytecode writing
  PYTHONDONTWRITEBYTECODE = "1"
  # Set logging level
  LOG_LEVEL = "INFO"
  # Enable CORS for frontend
  CORS_ORIGINS = "https://app.pilawyer.ai,https://pilawyer.ai"
  # Enable AG-UI protocol
  NEXT_PUBLIC_AGUI_ENABLED = "true"

# Autoscaling configuration
[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 5 # Maximum number of instances for scaling
  processes = ["app"]

  # Concurrency settings
  [http_service.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 500

  # Health check configuration
  [[http_service.checks]]
    interval = "30s"
    timeout = "5s"
    grace_period = "30s" # Increased to allow for LangGraph initialization
    method = "GET"
    path = "/health"
    protocol = "http"
    tls_skip_verify = false

  # Restart policy
  [http_service.restart_policy]
    min_seconds_between_restarts = 60
    max_restarts_per_hour = 5
    restart_on = "unhealthy"

# Metrics endpoint for monitoring
[metrics]
  port = 8000
  path = "/metrics"

# Additional HTTP service for metrics (can reuse port 8000)
[[services]]
  internal_port = 8000
  protocol = "tcp"

  [[services.ports]]
    port = 8000
    handlers = ["http"]

  [[services.http_checks]]
    interval = "30s"
    timeout = "5s"
    method = "GET"
    path = "/metrics"

# Deployment configuration
[deploy]
  release_command = "python -m alembic upgrade head"
  strategy = "rolling" # Rolling deployment for zero downtime
  max_unavailable = 0 # Ensure no downtime during deployments

# Process definitions
[processes]
  app = "uvicorn src.pi_lawyer.api.runtime:app --host 0.0.0.0 --port 8000 --workers 2"

# Persistent storage for LangGraph state and embeddings
[mounts]
  source = "pi_lawyer_data"
  destination = "/app/data"

# Specify secrets that need to be set in the Fly.io dashboard
# These are NOT stored in this file for security reasons
[secrets]
  # OpenAI
  OPENAI_API_KEY = ""

  # Pinecone
  PINECONE_API_KEY = ""
  PINECONE_ENVIRONMENT = ""
  PINECONE_INDEX_NAME = ""

  # Supabase
  SUPABASE_URL = ""
  SUPABASE_KEY = ""
  SUPABASE_JWT_SECRET = "" # Added for JWT verification

  # CopilotKit
  CPK_ENDPOINT_SECRET = ""
  COPILOTKIT_API_KEY = "" # Added for CopilotKit Cloud

  # AG-UI Configuration
  CLOUD_AGENT_ID_SUPERVISOR = "" # Added for AG-UI protocol
  CLOUD_AGENT_ID_INTAKE = ""
  CLOUD_AGENT_ID_DOCUMENT = ""
  CLOUD_AGENT_ID_RESEARCH = ""
  CLOUD_AGENT_ID_DEADLINE = ""
  CLOUD_AGENT_ID_EVENT = ""

  # Database
  DB_PASSWORD = ""
  DB_USER = ""
  DB_NAME = ""
  DB_HOST = ""
  DB_PORT = ""

  # LangSmith for tracing and monitoring
  LANGSMITH_API_KEY = ""
  LANGSMITH_PROJECT = "pi-lawyer-langgraph-production"

  # Voyage AI for embeddings
  VOYAGE_API_KEY = ""

  # Neo4j Configuration
  NEO4J_URI = ""
  NEO4J_USER = ""
  NEO4J_PASSWORD = ""
  ENABLE_NEO4J = "true"

  # Redis Configuration
  REDIS_URL = ""
  REDIS_PASSWORD = ""
  REDIS_QUEUE_PREFIX = "pi_lawyer_docs"

  # Additional AI Services
  GROQ_API_KEY = ""
  GEMINI_API_KEY = ""
