# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Authentication Configuration
# Set to 'true' to use Supabase Auth, 'false' to use legacy cookie/JWT auth
NEXT_PUBLIC_USE_SUPABASE_AUTH=false
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=new-texas-laws
GCS_BUCKET_NAME=texas-laws-personalinjury
GCS_SERVICE_ACCOUNT_FILE=/path/to/service-account.json
OPENAI_API_KEY=your_openai_api_key
SUPABASE_ACCESS_TOKEN=your_supabase_access_token
SUPABASE_JWT_SECRET=your_jwt_secret
DB_PASSWORD=your_db_password
DB_USER=postgres
DB_NAME=postgres
DB_HOST=your_db_host
DB_PORT=5432
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key
TURNSTILE_SECRET_KEY=your_turnstile_secret_key

# CopilotKit Configuration
NEXT_PUBLIC_COPILOT_API_KEY=your_copilot_api_key

# LLM Selection
DEFAULT_LLM_MODEL=openai/gpt-3.5-turbo
DEFAULT_LLM_TEMPERATURE=0.2

# Queue Monitoring Configuration
QUEUE_POLLING_INTERVAL=5000

# Voice Metrics Configuration
NEXT_PUBLIC_GRAFANA_URL=https://grafana.ailex.com/d/avr/voice-metrics
NEXT_PUBLIC_PROMETHEUS_PROXY_URL=/api/prometheus

# Stripe Configuration (for subscription billing)
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Avricons Intake Webhook Configuration
CORE_INTAKE_SECRET=your_core_intake_secret_key

# Optional: Redis Queue Configuration
USE_REDIS_QUEUE=false

# Note: Prospect Management is implemented in website repo (ailexlaw.com)
# The variables below are for reference only - not used in this repo

# Prospect Management Configuration (WEBSITE REPO ONLY)
# NEXT_PUBLIC_SITE_URL=https://www.ailexlaw.com
# PROSPECT_EMAIL_SERVICE=resend
# RESEND_API_KEY=your_resend_api_key

# GDPR Compliance (WEBSITE REPO ONLY)
# GDPR_DATA_RETENTION_DAYS=365
# AUTO_CLEANUP_PROSPECTS=true
