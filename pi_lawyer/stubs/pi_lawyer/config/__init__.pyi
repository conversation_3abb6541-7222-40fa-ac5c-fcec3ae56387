"""
Type stubs for pi_lawyer.config module.
These are minimal type definitions to satisfy mypy.
"""

from typing import Any, Dict, Optional, List
import os

# Common environment variables - using values specified in user requirements
SUPABASE_URL: str
SUPABASE_SERVICE_KEY: str
NEXT_PUBLIC_SUPABASE_ANON_KEY: str
PINECONE_API_KEY: str
PINECONE_ENVIRONMENT: str = "us-east-1"
PINECONE_INDEX_NAME: str = "new-texas-laws"
GCS_BUCKET_NAME: str = "texas-laws-personalinjury"
GCS_SERVICE_ACCOUNT_FILE: str = (
    "/Users/<USER>/Documents/Texas/texas-laws-personalinjury-16bfba9a4057.json"
)
OPENAI_API_KEY: str
SUPABASE_ACCESS_TOKEN: str
SUPABASE_JWT_SECRET: str
DB_PASSWORD: str
DB_USER: str = "postgres"
DB_NAME: str = "postgres"
DB_HOST: str = "db.anwefmklplkjxkmzpnva.supabase.co"
DB_PORT: int = 5432
NEXT_PUBLIC_TURNSTILE_SITE_KEY: str
TURNSTILE_SECRET_KEY: str

# Common configuration functions
def get_config() -> Dict[str, Any]: ...
def get_database_url() -> str: ...
def get_supabase_url() -> str: ...
def get_supabase_key() -> str: ...
def get_service_key() -> str: ...
def get_jwt_secret() -> str: ...
