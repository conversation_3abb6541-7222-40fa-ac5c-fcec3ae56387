"""
Supabase client module for PI Lawyer AI.

This module provides a typed Supabase client for accessing the database.
"""

import os
from typing import Optional, Dict, Any, Callable, TypeVar, cast
from functools import lru_cache
import logging
from supabase import create_client, Client

from ..utils.structured_logging import log_with_context

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for generic functions
T = TypeVar("T")


@lru_cache()
def get_supabase_client() -> Client:
    """
    Get a cached Supabase client instance.

    This function uses lru_cache to avoid creating multiple clients.

    Returns:
        Client: A configured Supabase client

    Raises:
        ValueError: If Supabase credentials are not configured
    """
    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_key = os.environ.get("SUPABASE_SERVICE_KEY")

    if not supabase_url or not supabase_key:
        error_msg = "Supabase credentials not configured"
        log_with_context(logger, "error", error_msg)
        raise ValueError(error_msg)

    try:
        client = create_client(supabase_url, supabase_key)
        log_with_context(logger, "info", "Supabase client created successfully")
        return client
    except Exception as e:
        error_msg = f"Failed to create Supabase client: {str(e)}"
        log_with_context(logger, "error", error_msg)
        raise


def get_service_client() -> Client:
    """
    Get a Supabase client with service role credentials.

    This client has elevated permissions and should be used carefully.

    Returns:
        Client: A Supabase client with service role permissions

    Raises:
        ValueError: If Supabase service credentials are not configured
    """
    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_service_key = os.environ.get("SUPABASE_ACCESS_TOKEN")

    if not supabase_url or not supabase_service_key:
        error_msg = "Supabase service credentials not configured"
        log_with_context(logger, "error", error_msg)
        raise ValueError(error_msg)

    try:
        client = create_client(supabase_url, supabase_service_key)
        log_with_context(logger, "info", "Supabase service client created")
        return client
    except Exception as e:
        error_msg = f"Failed to create Supabase service client: {str(e)}"
        log_with_context(logger, "error", error_msg)
        raise


def with_error_handling(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator to handle Supabase errors consistently.

    Args:
        func: The function to wrap with error handling

    Returns:
        Callable: The wrapped function with error handling
    """

    def wrapper(*args: Any, **kwargs: Any) -> T:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Log the error
            log_with_context(
                logger,
                "error",
                f"Supabase operation failed: {str(e)}",
                function=func.__name__,
            )
            # Re-raise the exception
            raise

    return wrapper
